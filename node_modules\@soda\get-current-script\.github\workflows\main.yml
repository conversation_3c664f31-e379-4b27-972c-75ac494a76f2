# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run. Triggers the workflow on push or pull request 
# events but only for the master branch
on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: windows-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
    # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v1
      with:
        node-version: '12.x'


    # Runs a single command using the runners shell
    - name: Install dependencies
      run: yarn --frozen-lockfile

    # Runs a set of commands using the runners shell
    # As there seems to be issues like "socket hang up" in the github actions environment when testing all browsers altogether, I run the browser tests sequentially in the ci script
    - name: Run the test
      run: yarn ci
