// 涂鸦插件工具类
// 封装涂鸦小程序插件的调用方法

/**
 * 配网插件工具类
 */
export class TuyaNetworkPlugin {
  /**
   * 初始化配网插件
   */
  static init() {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      const plugin = requirePlugin('tuya-ap-plugin')
      return plugin
    }
    // #endif
    return null
  }

  /**
   * 开始配网
   * @param {Object} options 配网参数
   */
  static async startNetworkConfig(options = {}) {
    try {
      const plugin = this.init()
      if (!plugin) {
        throw new Error('配网插件未初始化')
      }

      const defaultOptions = {
        mode: 'ap', // ap: 热点配网, ble: 蓝牙配网, qr: 扫码配网
        ssid: '', // WiFi名称
        password: '', // WiFi密码
        timeout: 60000, // 超时时间
        ...options
      }

      return await plugin.startConfig(defaultOptions)
    } catch (error) {
      console.error('配网失败:', error)
      throw error
    }
  }

  /**
   * 停止配网
   */
  static async stopNetworkConfig() {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.stopConfig()
      }
    } catch (error) {
      console.error('停止配网失败:', error)
      throw error
    }
  }

  /**
   * 获取配网状态
   */
  static async getNetworkStatus() {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.getStatus()
      }
    } catch (error) {
      console.error('获取配网状态失败:', error)
      throw error
    }
  }
}

/**
 * 消息中心插件工具类
 */
export class TuyaMessagePlugin {
  /**
   * 初始化消息中心插件
   */
  static init() {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      const plugin = requirePlugin('tuya-message-center')
      return plugin
    }
    // #endif
    return null
  }

  /**
   * 获取消息列表
   * @param {Object} params 查询参数
   */
  static async getMessageList(params = {}) {
    try {
      const plugin = this.init()
      if (!plugin) {
        throw new Error('消息中心插件未初始化')
      }

      const defaultParams = {
        page: 1,
        limit: 20,
        type: 'all', // all: 全部, alarm: 告警, notice: 通知
        ...params
      }

      return await plugin.getMessageList(defaultParams)
    } catch (error) {
      console.error('获取消息列表失败:', error)
      throw error
    }
  }

  /**
   * 标记消息为已读
   * @param {Array} messageIds 消息ID数组
   */
  static async markAsRead(messageIds) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.markAsRead(messageIds)
      }
    } catch (error) {
      console.error('标记消息已读失败:', error)
      throw error
    }
  }

  /**
   * 删除消息
   * @param {Array} messageIds 消息ID数组
   */
  static async deleteMessages(messageIds) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.deleteMessages(messageIds)
      }
    } catch (error) {
      console.error('删除消息失败:', error)
      throw error
    }
  }

  /**
   * 获取未读消息数量
   */
  static async getUnreadCount() {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.getUnreadCount()
      }
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
      throw error
    }
  }
}

/**
 * 场景插件工具类
 */
export class TuyaScenePlugin {
  /**
   * 初始化场景插件
   */
  static init() {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      const plugin = requirePlugin('tuya-auto-plugin')
      return plugin
    }
    // #endif
    return null
  }

  /**
   * 获取场景列表
   * @param {Object} params 查询参数
   */
  static async getSceneList(params = {}) {
    try {
      const plugin = this.init()
      if (!plugin) {
        throw new Error('场景插件未初始化')
      }

      const defaultParams = {
        homeId: '',
        page: 1,
        limit: 20,
        ...params
      }

      return await plugin.getSceneList(defaultParams)
    } catch (error) {
      console.error('获取场景列表失败:', error)
      throw error
    }
  }

  /**
   * 执行场景
   * @param {String} sceneId 场景ID
   */
  static async executeScene(sceneId) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.executeScene(sceneId)
      }
    } catch (error) {
      console.error('执行场景失败:', error)
      throw error
    }
  }

  /**
   * 创建场景
   * @param {Object} sceneData 场景数据
   */
  static async createScene(sceneData) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.createScene(sceneData)
      }
    } catch (error) {
      console.error('创建场景失败:', error)
      throw error
    }
  }

  /**
   * 更新场景
   * @param {String} sceneId 场景ID
   * @param {Object} sceneData 场景数据
   */
  static async updateScene(sceneId, sceneData) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.updateScene(sceneId, sceneData)
      }
    } catch (error) {
      console.error('更新场景失败:', error)
      throw error
    }
  }

  /**
   * 删除场景
   * @param {String} sceneId 场景ID
   */
  static async deleteScene(sceneId) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.deleteScene(sceneId)
      }
    } catch (error) {
      console.error('删除场景失败:', error)
      throw error
    }
  }
}

/**
 * 通用设备面板插件工具类
 */
export class TuyaPanelPlugin {
  /**
   * 初始化设备面板插件
   */
  static init() {
    // #ifdef MP-WEIXIN
    if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
      const plugin = requirePlugin('tuya-panel-plugin')
      return plugin
    }
    // #endif
    return null
  }

  /**
   * 打开设备面板
   * @param {Object} deviceInfo 设备信息
   */
  static async openDevicePanel(deviceInfo) {
    try {
      const plugin = this.init()
      if (!plugin) {
        throw new Error('设备面板插件未初始化')
      }

      const params = {
        deviceId: deviceInfo.id,
        deviceType: deviceInfo.type,
        productId: deviceInfo.productId || '',
        ...deviceInfo
      }

      return await plugin.openPanel(params)
    } catch (error) {
      console.error('打开设备面板失败:', error)
      throw error
    }
  }

  /**
   * 获取设备面板配置
   * @param {String} deviceId 设备ID
   */
  static async getPanelConfig(deviceId) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.getPanelConfig(deviceId)
      }
    } catch (error) {
      console.error('获取设备面板配置失败:', error)
      throw error
    }
  }

  /**
   * 控制设备
   * @param {String} deviceId 设备ID
   * @param {Object} commands 控制命令
   */
  static async controlDevice(deviceId, commands) {
    try {
      const plugin = this.init()
      if (plugin) {
        return await plugin.controlDevice(deviceId, commands)
      }
    } catch (error) {
      console.error('控制设备失败:', error)
      throw error
    }
  }
}
