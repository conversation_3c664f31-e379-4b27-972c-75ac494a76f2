<template>
  <view class="container">
    <!-- 添加设备说明 -->
    <view class="guide-card bg-white rounded shadow">
      <view class="guide-title">添加设备</view>
      <view class="guide-desc">请确保设备已通电并处于配网模式</view>
      <view class="guide-steps">
        <view class="step-item">
          <text class="step-num">1</text>
          <text class="step-text">确保设备已通电</text>
        </view>
        <view class="step-item">
          <text class="step-num">2</text>
          <text class="step-text">长按设备上的配网按钮，直到指示灯快速闪烁</text>
        </view>
        <view class="step-item">
          <text class="step-num">3</text>
          <text class="step-text">选择设备类型并开始配网</text>
        </view>
      </view>
    </view>

    <!-- 设备类型选择 -->
    <view class="section-title">选择设备类型</view>
    <view class="device-types bg-white rounded shadow">
      <view
        class="device-type-item"
        v-for="(type, index) in deviceTypes"
        :key="index"
        @click="selectDeviceType(type)"
      >
        <view class="device-type-icon">{{ type.icon }}</view>
        <text class="device-type-name">{{ type.name }}</text>
      </view>
    </view>

    <!-- 房间选择 -->
    <view class="section-title">选择房间</view>
    <view class="room-selector bg-white rounded shadow">
      <picker
        mode="selector"
        :range="roomNames"
        @change="onRoomChange"
      >
        <view class="picker-content">
          <text>{{ selectedRoom || '请选择房间' }}</text>
          <text class="arrow-icon">▼</text>
        </view>
      </picker>
    </view>

    <!-- 开始配网按钮 -->
    <button
      class="start-btn"
      :disabled="!selectedDeviceType || !selectedRoom"
      @click="startPairing"
    >开始配网</button>
  </view>
</template>

<script>
import { TuyaNetworkPlugin } from '@/utils/tuya-plugins.js'

export default {
  data() {
    return {
      deviceTypes: [
        { id: 'light', name: '智能灯', icon: '💡' },
        { id: 'airconditioner', name: '空调', icon: '❄️' },
        { id: 'humidifier', name: '加湿器', icon: '💧' },
        { id: 'switch', name: '智能开关', icon: '🔌' },
        { id: 'curtain', name: '窗帘', icon: '🪟' },
        { id: 'camera', name: '摄像头', icon: '📹' }
      ],
      rooms: [
        { id: '1', name: '客厅' },
        { id: '2', name: '卧室' },
        { id: '3', name: '厨房' },
        { id: '4', name: '浴室' },
        { id: '5', name: '书房' }
      ],
      selectedDeviceType: null,
      selectedRoom: '',
      selectedRoomId: ''
    }
  },
  computed: {
    roomNames() {
      return this.rooms.map(room => room.name)
    }
  },
  methods: {
    selectDeviceType(type) {
      this.selectedDeviceType = type
    },
    onRoomChange(e) {
      const index = e.detail.value
      this.selectedRoom = this.rooms[index].name
      this.selectedRoomId = this.rooms[index].id
    },
    async startPairing() {
      if (!this.selectedDeviceType || !this.selectedRoom) {
        return
      }

      try {
        // 显示配网中的加载提示
        uni.showLoading({
          title: '正在配网...',
          mask: true
        })

        // 使用涂鸦配网插件进行配网
        const networkResult = await this.startTuyaNetworkConfig()

        if (networkResult && networkResult.success) {
          // 配网成功，添加设备到本地
          const newDevice = {
            id: networkResult.deviceId || `device_${Date.now()}`,
            name: `${this.selectedRoom}${this.selectedDeviceType.name}`,
            type: this.selectedDeviceType.id,
            status: 'online',
            data: this.getDefaultDeviceData(this.selectedDeviceType.id),
            homeId: this.$store.state.currentHome.id,
            roomName: this.selectedRoom,
            productId: networkResult.productId || ''
          }

          this.$store.commit('ADD_DEVICE', newDevice)

          uni.hideLoading()

          // 显示添加成功提示
          uni.showToast({
            title: '设备添加成功',
            icon: 'success'
          })

          // 返回首页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error('配网失败')
        }
      } catch (error) {
        uni.hideLoading()

        console.error('配网失败:', error)

        // 如果是真实环境配网失败，回退到模拟配网
        this.fallbackToMockPairing()
      }
    },

    // 使用涂鸦配网插件
    async startTuyaNetworkConfig() {
      try {
        // 获取当前WiFi信息（在真实环境中需要用户输入）
        const wifiInfo = await this.getWifiInfo()

        const configOptions = {
          mode: 'ap', // 默认使用热点配网
          ssid: wifiInfo.ssid,
          password: wifiInfo.password,
          timeout: 60000
        }

        return await TuyaNetworkPlugin.startNetworkConfig(configOptions)
      } catch (error) {
        console.error('涂鸦配网失败:', error)
        throw error
      }
    },

    // 获取WiFi信息（模拟）
    async getWifiInfo() {
      // 在真实环境中，这里应该弹出输入框让用户输入WiFi信息
      // 或者通过系统API获取当前连接的WiFi信息
      return new Promise((resolve) => {
        uni.showModal({
          title: 'WiFi配置',
          content: '请确保手机已连接到要配置的WiFi网络',
          showCancel: false,
          success: () => {
            // 模拟WiFi信息
            resolve({
              ssid: 'MyWiFi',
              password: '12345678'
            })
          }
        })
      })
    },

    // 回退到模拟配网
    fallbackToMockPairing() {
      uni.showLoading({
        title: '模拟配网中...',
        mask: true
      })

      // 模拟配网过程
      setTimeout(() => {
        uni.hideLoading()

        // 模拟添加设备
        const newDevice = {
          id: `device_${Date.now()}`,
          name: `${this.selectedRoom}${this.selectedDeviceType.name}`,
          type: this.selectedDeviceType.id,
          status: 'online',
          data: this.getDefaultDeviceData(this.selectedDeviceType.id),
          homeId: this.$store.state.currentHome.id,
          roomName: this.selectedRoom
        }

        this.$store.commit('ADD_DEVICE', newDevice)

        // 显示添加成功提示
        uni.showToast({
          title: '设备添加成功（模拟）',
          icon: 'success'
        })

        // 返回首页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }, 3000)
    },
    getDefaultDeviceData(deviceType) {
      // 根据设备类型返回默认数据
      switch (deviceType) {
        case 'light':
          return { power: false, brightness: 80 }
        case 'airconditioner':
          return { power: false, temperature: 26, mode: 'cool' }
        case 'humidifier':
          return { power: false, humidity: 50 }
        case 'switch':
          return { power: false }
        case 'curtain':
          return { power: false, position: 0 }
        case 'camera':
          return { power: false, recording: false }
        default:
          return { power: false }
      }
    }
  }
}
</script>

<style>
.container {
  padding: 30rpx;
}

.guide-card {
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.guide-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.guide-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
}

.guide-steps {
  margin-top: 20rpx;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.step-num {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #0066FF;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.step-text {
  font-size: 28rpx;
  color: #333333;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 30rpx 0 20rpx;
}

.device-types {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.device-type-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.device-type-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.device-type-name {
  font-size: 24rpx;
  color: #333333;
}

.room-selector {
  padding: 20rpx 30rpx;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
}

.arrow-icon {
  color: #999999;
  font-size: 24rpx;
}

.start-btn {
  margin-top: 60rpx;
  background-color: #0066FF;
  color: #FFFFFF;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.start-btn[disabled] {
  background-color: #CCCCCC;
  color: #FFFFFF;
}
</style>
